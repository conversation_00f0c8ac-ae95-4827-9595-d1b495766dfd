"use client";
import React, { useState, useRef, useEffect } from "react";
import { Menu, X, ChevronDown } from "lucide-react";
import Link from "next/link";
import json from "./team.json";
import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";
import { TwitterLogoIcon, LinkedInLogoIcon, GitHubLogoIcon, InstagramLogoIcon } from "@radix-ui/react-icons";
import Footer from "../Footer/page";
import NavBar from "../NavBar/page";

const Team = () => {
  const teamArray = json.teamArray;

  const Gen = [
    { label: "President", key: "p" },
    { label: "Alumni", key: "a" },
    { label: "Gen 1", key: "1" },
    { label: "Gen 2", key: "2" },
    { label: "Gen 3", key: "3" }
  ];

  const [selectedGen, setSelectedGen] = useState("p");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside or pressing Escape
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  const handleGenSelection = (genKey) => {
    setSelectedGen(genKey);
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const selectedGenLabel = Gen.find(gen => gen.key === selectedGen)?.label || "President";

  return (
    <div className="flex flex-col min-h-screen bg-gray-100 dark:bg-neutral-800">
        <NavBar/>
      {/* Mobile Header with Generations Dropdown */}
      <div className="md:hidden sticky top-0 z-50 bg-white dark:bg-neutral-900 shadow-sm">
        <div className="flex justify-between items-center p-4">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Team</h2>

          {/* Custom Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <Button
              variant="outline"
              onClick={toggleDropdown}
              className={`flex items-center gap-2 min-w-[120px] justify-between transition-all duration-200 ${
                isDropdownOpen ? 'ring-2 ring-blue-500 border-blue-500' : ''
              }`}
            >
              <span className="truncate">{selectedGenLabel}</span>
              <ChevronDown
                className={`h-4 w-4 transition-transform duration-200 ${
                  isDropdownOpen ? 'rotate-180' : ''
                }`}
              />
            </Button>

            {/* Backdrop overlay for mobile */}
            {isDropdownOpen && (
              <div
                className="fixed inset-0 bg-black/20 z-40 md:hidden"
                onClick={() => setIsDropdownOpen(false)}
              />
            )}

            {/* Dropdown Menu */}
            {isDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-neutral-900 border border-gray-200 dark:border-neutral-700 rounded-md shadow-xl z-50 transform transition-all duration-200 ease-out animate-in slide-in-from-top-2 fade-in-0">
                <div className="py-1">
                  {Gen.map((gen, index) => (
                    <button
                      key={gen.key}
                      onClick={() => handleGenSelection(gen.key)}
                      className={`w-full text-left px-4 py-3 text-sm transition-all duration-150 hover:translate-x-1 ${
                        selectedGen === gen.key
                          ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 font-medium border-l-2 border-blue-500'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-neutral-800'
                      }`}
                    >
                      {gen.label}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-1">
        {/* Desktop Sidebar */}
        <div className="hidden md:flex w-64 bg-white dark:bg-neutral-900 p-6 flex-col border-r dark:border-neutral-700">
          <div className="mt-8 space-y-2">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Generations</h3>
            {Gen.map((gen) => (
              <Button
                key={gen.key}
                variant={selectedGen === gen.key ? "default" : "ghost"}
                onClick={() => handleGenSelection(gen.key)}
                className="w-full justify-start transition-all duration-200 hover:scale-105"
              >
                {gen.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Team Members Grid */}
        <div className="flex-1 p-4 md:p-6 overflow-y-auto sm:mt-10 md:mt-20 lg:mt-25">
          {teamArray.filter(member => member.gen === selectedGen).length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <div className="text-6xl mb-4">👥</div>
              <h3 className="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">
                No members found
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                There are currently no members in the {selectedGenLabel} category.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {teamArray.filter(member => member.gen === selectedGen).map(member => (
              <div
                key={member.name}
                className="flex flex-col items-center text-center gap-4 bg-white dark:bg-neutral-900 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="h-32 w-32 rounded-full object-cover border-4 border-gray-200 dark:border-neutral-700"
                />
                <div>
                  <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200">
                    {member.name}
                  </h3>
                  {member.role && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {member.role}
                    </p>
                  )}
                  <div className="mt-4 flex justify-center gap-3">
                    {member.tw && (
                      <Button
                        variant="outline"
                        size="icon"
                        asChild
                        className="text-blue-500 hover:bg-blue-500"
                      >
                        <Link href={member.tw} target="_blank" rel="noopener noreferrer">
                          <TwitterLogoIcon className="h-5 w-5" />
                        </Link>
                      </Button>
                    )}
                    {member.li && (
                      <Button
                        variant="outline"
                        size="icon"
                        asChild
                        className="text-indigo-500 hover:bg-indigo-500"
                      >
                        <Link href={member.li} target="_blank" rel="noopener noreferrer">
                          <LinkedInLogoIcon className="h-5 w-5" />
                        </Link>
                      </Button>
                    )}
                    {member.gh && (
                      <Button
                        variant="outline"
                        size="icon"
                        asChild
                        className="text-indigo-500 hover:bg-black-500"
                      >
                        <Link href={member.gh} target="_blank" rel="noopener noreferrer">
                          <GitHubLogoIcon className="h-5 w-5" />
                        </Link>
                      </Button>
                    )}
                    {member.ig && (
                      <Button
                        variant="outline"
                        size="icon"
                        asChild
                        className="text-indigo-500 hover:bg-red-500"
                      >
                        <Link href={member.ig} target="_blank" rel="noopener noreferrer">
                          <InstagramLogoIcon className="h-5 w-5" />
                        </Link>
                      </Button>
                    )}
                    {member.em && (
                      <Button
                        variant="outline"
                        size="icon"
                        asChild
                        className="text-pink-500 hover:bg-pink-500"
                      >
                        <Link href={`mailto:${member.em}`} target="_blank" rel="noopener noreferrer">
                          <Mail className="h-5 w-5" />
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>
              </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}

// export const Logo = () => (
//   <Link href="#" className="flex space-x-2 items-center text-lg font-bold text-black dark:text-white">
//     <div className="h-6 w-7 bg-black dark:bg-white rounded-lg" />
//     <span className="whitespace-pre">Coding Junction</span>
//   </Link>
// );

export default Team;